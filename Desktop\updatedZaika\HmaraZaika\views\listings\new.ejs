<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Z<PERSON><PERSON>_Zunction</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        label {
            font-weight: bold;
            font-size: 20px;
        }
        h1 {
            text-align: center;
        }
        form {
            margin: 20px 0;
        }
        body {
            background-color: #bfd54e6c;
        }
        .form-control {
            border-radius: 0.4rem;
        }
    </style>
</head>
<% layout("/layouts/boilerplate") %> 
<body>
    <!-- Form to create new food item -->
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <h1>New Food Item</h1>
                <form method="POST" action="/listings" class="needs-validation" novalidate enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="name" class="form-label">Name:</label>
                        <input id="name" name="foodListing[name]" placeholder="Enter name" type="text" class="form-control" required>
                        <div class="valid-feedback">
                            Looks good!
                        </div>
                        <div class="invalid-feedback">
                            Please provide a name.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="ingredients" class="form-label">Ingredients:</label>
                        <textarea id="ingredients" name="foodListing[ingredients]" placeholder="Enter ingredients" class="form-control" rows="3" required></textarea>
                        <div class="valid-feedback">
                            Looks good!
                        </div>
                        <div class="invalid-feedback">
                            Please provide the ingredients.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="price" class="form-label">Price:</label>
                        <input id="price" name="foodListing[price]" placeholder="Enter Price" type="number" class="form-control" required>
                        <div class="valid-feedback">
                            Looks good!
                        </div>
                        <div class="invalid-feedback">
                            Please provide a valid price.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="location" class="form-label">Location:</label>
                        <input id="location" name="foodListing[location]" placeholder="Enter Location" type="text" class="form-control" required>
                        <div class="valid-feedback">
                            Looks good!
                        </div>
                        <div class="invalid-feedback">
                            Please provide a location.
                        </div>
                    </div>
                     
                    <!-- <div class="mb-3">
                        <label for="images" class="form-label">Image Link:</label>
                        <input id="images" name="foodListing[images]" placeholder="Enter Image Link" type="text" class="form-control" required>
                        <div class="valid-feedback">
                            Looks good!
                        </div>
                        <div class="invalid-feedback">
                            Please provide an image link.
                        </div>
                    </div> -->
                    <div class="mb-3">
                        <label for="images" class="form-label">Upload Food Image:</label>
                        <input id="images" name="foodListing[image]"  type="file" class="form-control" required>
                        <div class="valid-feedback">
                            Looks good!
                        </div>
                        <div class="invalid-feedback">
                            Please provide an image link.
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="DeliveryCharge" class="form-label">Delivery Charge:</label>
                        <input id="DeliveryCharge" name="foodListing[DeliveryCharge]" placeholder="Enter Delivery Charge (optional)" type="number" class="form-control">
                        <div class="valid-feedback">
                            Looks good!
                        </div>
                        <div class="invalid-feedback">
                            Please provide a valid delivery charge.
                        </div>
                    </div>
                    
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-success btn-block"><b>Add</b></button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS for validation -->
    <script>
        (function () {
            'use strict'

            var forms = document.querySelectorAll('.needs-validation')

            Array.prototype.slice.call(forms)
                .forEach(function (form) {
                    form.addEventListener('submit', function (event) {
                        if (!form.checkValidity()) {
                            event.preventDefault()
                            event.stopPropagation()
                        }

                        form.classList.add('was-validated')
                    }, false)
                })
        })()
    </script>
</body>
</html>
