{"engines": {"node": "20.17.0"}, "name": "miniprojectfood", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@mapbox/mapbox-sdk": "^0.16.1", "cloudinary": "^2.5.1", "connect-flash": "^0.1.1", "connect-mongo": "^5.1.0", "dotenv": "^16.4.5", "ejs": "^3.1.10", "ejs-mate": "^4.0.0", "express": "^4.21.0", "express-session": "^1.18.1", "joi": "^17.13.3", "method-override": "^3.0.0", "mongoose": "^8.6.2", "multer": "^1.4.5-lts.1", "multer-storage-cloudinary": "^4.0.0", "passport": "^0.7.0", "passport-local": "^1.0.0", "passport-local-mongoose": "^8.0.0"}}