<% layout("/layouts/boilerplate") %>
<body style="background-color: #bfd54e6c;">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <h1 class="text-center my-4"><b>Edit Your Food Item</b></h1>

                <!-- Form for editing a food listing -->
                <form method="POST" action="/listings/<%= foodListing._id %>?_method=PUT" class="needs-validation" novalidate enctype="multipart/form-data">
                    
                    <!-- Name -->
                    <div class="mb-3">
                        <label for="name" class="form-label" style="font-size: 20px;">Name:</label>
                        <input id="name" name="foodListing[name]" value="<%= foodListing.name %>" type="text" class="form-control" required>
                        <div class="invalid-feedback">Please provide a name.</div>
                    </div>

                    <!-- Ingredients -->
                    <div class="mb-3">
                        <label for="ingredients" class="form-label" style="font-size: 20px;">Ingredients:</label>
                        <textarea id="ingredients" name="foodListing[ingredients]" class="form-control" rows="3" required><%= foodListing.ingredients %></textarea>
                        <div class="invalid-feedback">Please provide ingredients.</div>
                    </div>

                    <!-- Price -->
                    <div class="mb-3">
                        <label for="price" class="form-label" style="font-size: 20px;">Price:</label>
                        <input id="price" name="foodListing[price]" value="<%= foodListing.price %>" type="number" class="form-control" required>
                        <div class="invalid-feedback">Please provide a valid price.</div>
                    </div>

                    <!-- Location -->
                    <div class="mb-3">
                        <label for="location" class="form-label" style="font-size: 20px;">Location:</label>
                        <input id="location" name="foodListing[location]" value="<%= foodListing.location %>" type="text" class="form-control" required>
                        <div class="invalid-feedback">Please provide a location.</div>
                    </div>

                    <!-- Delivery Charge -->
                    <div class="mb-3">
                        <label for="DeliveryCharge" class="form-label" style="font-size: 20px;">Delivery Charge:</label>
                        <input type="number" name="foodListing[DeliveryCharge]" value="<%= foodListing.DeliveryCharge %>" class="form-control" required>
                        <div class="invalid-feedback">Please provide a delivery charge.</div>
                    </div>

                    <!-- Image Link -->
                    <div class="mb-3">
                        <label for="image" class="form-label" style="font-size: 20px;">Upload New Image</label>
                        <input type="file" name="foodListing[images]" id="image" class="form-control" >
                        <div class="invalid-feedback">Please provide a valid image link.</div>
                    </div>

                    <!-- Submit Button -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-success btn-block">Update Listing</button>
                        <br>
                        <br>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS for form validation -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // JavaScript for disabling form submissions if there are invalid fields
        (function () {
            'use strict'
            var forms = document.querySelectorAll('.needs-validation')
            Array.prototype.slice.call(forms)
                .forEach(function (form) {
                    form.addEventListener('submit', function (event) {
                        if (!form.checkValidity()) {
                            event.preventDefault()
                            event.stopPropagation()
                        }
                        form.classList.add('was-validated')
                    }, false)
                })
        })()
    </script>
</body>
