<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navbar Example</title>
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Times New Roman', Times, serif;
        }

        img {
            height: 50px;
            width: 120%;
        }

        .navbar {
            background-color: #020202;
            border-bottom: 4px solid white;
        }

        .navbar a {
            color: white;
            font-weight: bold;
        }

        .navbar {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
        }

        .navbar-nav {
            margin-left: auto;
        }

        .navbar-nav .nav-link {
            margin-left: 30px;
            font-weight: bold;
        }

        .dropdown-menu {
            background-color: black;
        }

        .navbar-nav .nav-item {
            display: flex;
            align-items: center;
        }

        .navbar-nav .nav-item:first-child .nav-link {
            margin-left: 0;
        }

        .search-bar {
            display: flex;
            align-items: center;
            margin-right: auto;
            margin-left: 15px;
            
            transition: all 0.5s ease;
        }
        .search-bar input {
            border-radius: 20px;
            background-color: #ffffff;
            border: none;
            padding: 10px; /* Increased padding for height */
            margin-left: 10px;
            width: 200px;
            color: black;
            border:4px solid #757474;
            font-size: 21px; /* Set font size to medium */
            height: 45px; /* Added height */
            transition: width 0.5s ease, box-shadow 0.3s ease;
        }
        .search-bar.expanded input {
            width: 500px;
        }

        .nav-link {
            font-size: 20px;
        }

        .search-bar .search-icon {
            color: white;
            padding-left: 0.3rem;
            font-size: 24px;
            transition: font-size 0.5s ease;
        }

        .search-bar.expanded .search-icon {
            font-size: 28px;
        }

        .search-bar input:hover {
            box-shadow: 0 4px 8px gray;
            cursor: default;
        }

        .badge-custom {
            background-color: orange;
            color: white;
            font-size: 12px;
        }

        .btn-custom {
            margin-left: 1rem;
            font-weight: bold;
        }

        .hide-nav {
            opacity: 0;
            transition: opacity 0.5s ease;
        }
    </style>
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-dark sticky-top">
        <a class="navbar-brand" href="/listings"><img src="/uploads/Zaika.jpg" alt="Zaika"></a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNavDropdown"
            aria-controls="navbarNavDropdown" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNavDropdown">
            <div class="navbar-nav ml-auto">
                <li class="nav-item active">
                    <a class="nav-link" href="/listings">Home<span class="sr-only">(current)</span></a>
                </li>
                <li class="nav-item active">
                    <a class="nav-link" href='/listings/about'>About</a>
                </li>
                <li class="nav-item active">
                    <a class="nav-link" href="/listings/contact">Contact</a>
                </li>
                <li class="nav-item active">
                    <a class="nav-link" href="/listings/help">Help</a>
                </li>
                <li class="nav-item active">
                    <a class="nav-link" href="/listings/offers">Offers!</a>
                    
                </li>
                <li class="nav-item active">
                    <a class="nav-link" href="/orders/myOrders">My Orders</a>
                </li>
                
            </div>
            <div class="search-bar" id="searchBar">
                <input type="text" placeholder="Search your favourite">
                <i class="fas fa-search search-icon"></i>
            </div>
            <div class="navbar-nav">
                <% if (!currUser) { %>
                <li class="nav-item">
                    <a href="/signup" class="btn btn-primary btn-custom">Sign Up</a>
                </li>
                <li class="nav-item">
                    <a href="/login" class="btn btn-success btn-custom">Login</a>
                </li>
                <% } %>
                <% if (currUser) { %>
                <li class="nav-item">
                    <a href="/logout" class="btn btn-danger btn-custom">Logout</a>
                </li>
                <% } %>
            </div>
        </div>
    </nav>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/js/all.min.js"></script>

    <script>
        const searchBar = document.getElementById('searchBar');
        const searchInput = searchBar.querySelector('input');
        
        searchBar.addEventListener('click', function () {
            searchBar.classList.add('expanded');
        });

        searchInput.addEventListener('focus', function () {
            searchBar.classList.add('expanded');
        });

        searchBar.addEventListener('mouseleave', function () {
            if (searchInput.value === '') {
                searchBar.classList.remove('expanded');
            }
        });
    </script>
</body>

</html>
