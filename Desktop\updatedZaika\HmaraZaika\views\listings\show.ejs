<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Food Listing Details</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../../public/css/style.css">
    <style>
        #map {
            border:5px solid black;
            border-radius:20px;
            box-shadow:20px 15px 17px 5px grey;
            height: 400px; /* Set the height of the map */
            width: 100%; /* Make the map take up full width */
            margin: 0 auto; /* Center the map horizontally */
            display: block; /* Ensure the map is a block element */
            margin-right:13rem;
            margin-bottom:5rem;
        }

        .col-8.offset-3 {
            display: flex;
            justify-content: center; /* Center the map container */
            align-items: center; /* Center vertically */
            padding-top: 20px; /* Add some padding if needed */
        }

        .heading-container {
            text-align: center; /* Align heading in the center */
            margin-bottom: 20px; /* Add space between the heading and map */
        }
    </style>
</head>
<% layout("/layouts/boilerplate") %>
<script>
    let mapToken="<%=process.env.MAP_TOKEN%>";
    const coordinates=<%-JSON.stringify(currentFoodListing.geometry.coordinates)%>;
</script>
<body style="background-color: #bfd54e6c;">
    <div class="container">
        <h1>Food Item Details</h1>
        <!-- Food details section -->
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="food-details">
                    <ul>
                        <li><strong>Served By :</strong> <%= currentFoodListing.owner.username %></li>
                        <li><strong>Name of Food Item:</strong> <%= currentFoodListing.name %></li>
                        <li><strong>Price:</strong> Rs: <%= currentFoodListing.price %></li>
                        <li><strong>Ingredients:</strong> <%= currentFoodListing.ingredients %></li>
                        <li><strong>Delivery Charge:</strong> Rs: <%= currentFoodListing.DeliveryCharge %></li>
                        <li><strong>Location:</strong> <%= currentFoodListing.location %></li>
                    </ul>

                    <% if(currUser && currUser.equals(currentFoodListing.owner)){ %>
                    
                    <!-- Action buttons (Edit and Delete) -->
                    <div class="action-buttons">
                        <!-- Edit Button -->
                        <a href="/listings/<%= currentFoodListing._id %>/edit" class="btn btn-primary">Edit This Listing</a>
                        
                        <!-- Delete Button -->
                        <form method="POST" action="/listings/<%= currentFoodListing._id %>?_method=DELETE">
                            <button type="submit" class="btn btn-danger">Delete This Food Item</button>
                        </form>
                    </div>
                    <% } %>
                </div>
            </div>
        </div>

        <!-- Review section -->
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="review-section">
                    
                    <h4>How's the taste...</h4>
                    <% if(currUser){ %>
                    <form action="/listings/<%= currentFoodListing._id %>/reviews" class="needs-validation" novalidate method="POST">
                        <div class="mb-3">
                            <legend>rating:</legend>
                            <fieldset class="starability-coinFlip">
                                  
                                <input type="radio" id="no-rate" class="input-no-rate" name="review[rating]" value="1" checked aria-label="No rating." />
                                <input type="radio" id="first-rate1" name="review[rating]" value="1" required />
                                <label for="first-rate1" title="Terrible">1 star</label>
                                <input type="radio" id="first-rate2" name="review[rating]" value="2" />
                                <label for="first-rate2" title="Not good">2 stars</label>
                                <input type="radio" id="first-rate3" name="review[rating]" value="3" />
                                <label for="first-rate3" title="Average">3 stars</label>
                                <input type="radio" id="first-rate4" name="review[rating]" value="4" />
                                <label for="first-rate4" title="Very good">4 stars</label>
                                <input type="radio" id="first-rate5" name="review[rating]" value="5" />
                                <label for="first-rate5" title="Amazing">5 stars</label>
                            </fieldset>
                            <div class="invalid-feedback">
                                Please provide a rating.
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="comment">Comment</label>
                            <textarea name="review[comment]" id="comment" rows="5" class="form-control" required></textarea>
                            <div class="invalid-feedback">
                                Please provide a comment.
                            </div>
                        </div>
                        <button type="submit" class="btn btn-dark">Submit Review</button>
                    </form>
                    <% } %>
                </div>

                <hr>
                <h4>All Reviews</h4>

                <!-- Displaying reviews as cards -->
                <div class="review-cards-row">
                    <% if (currentFoodListing.reviews.length > 0) { %>
                        <% for (let review of currentFoodListing.reviews) { %>
                            <div class="review-card">
                                <h5>@<%= review.author ? review.author.username : "Anonymous" %></h5>
                                <p class="starability-result" data-rating="<%= review.rating %>">
                                    Rated: <%= review.rating %> stars
                                </p>
                                <p><%= review.comment %></p>
                                <p class="rating">Rating: <%= review.rating %> / 5</p>
                                  
                                <form action="/listings/<%= currentFoodListing._id %>/reviews/<%= review._id %>?_method=DELETE" class="mb-3" method="POST">
                                    <button class="btn btn-outline-danger"><b>Delete</b></button>
                                </form>                                  
                            </div>
                        <% } %>
                    <% } else { %>
                        <p>No reviews yet. Be the first to leave a review!</p>
                    <% } %>
                    
                </div>
            </div>
        </div>

        <!-- Move Heading Above the Map and Center the Map -->
        <div class="heading-container">
            <h1>Far From You Foodie !.</h1>
        </div>
        <div class="col-8 offset-3 mb-3">
            <div id="map"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <script src="/js/map.js">

    </script>
    <script>
        (function () {
            'use strict';
            var forms = document.querySelectorAll('.needs-validation');

            // Loop over them and prevent submission
            Array.prototype.slice.call(forms).forEach(function (form) {
                form.addEventListener('submit', function (event) {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }

                    form.classList.add('was-validated');
                }, false);
            });
        })();
    </script>
</body>
</html>
