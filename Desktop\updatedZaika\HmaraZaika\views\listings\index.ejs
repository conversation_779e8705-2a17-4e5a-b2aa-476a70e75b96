<% layout("/layouts/boilerplate") %>

    <head>
        <style>
            body {
                font-family: 'Times New Roman', Times, serif;
                background-color: #e8ffef53;
                margin: 0;
                padding: 0;
            }
            .card-body{
                border-bottom:2px solid black;
                border-left:2px solid black;
                border-right:2px solid black;
                /* background-color: white; */
            }
            h1{
                font-size:50px;
                font-weight: bold;
            }
            h1 span{
                color: rgb(255, 60, 0);
            }

            #orders{
                /* background-color: red; */

                font-size: 20px;
                border-radius: 20px;
                font-weight: bold;
                border:4px solid green;
                background-color: rgb(238, 238, 238);
            }
            #orders:hover{
                border:5px solid green;
            }
        </style>
    </head>

    <body>
        <h3 id="zaika-slogan">"Zaika Junction: Har <PERSON><PERSON>!"</h3>

        <div id="carouselExampleIndicators" class="carousel slide carousel-container" data-ride="carousel">
            <ol class="carousel-indicators">
                <li data-target="#carouselExampleIndicators" data-slide-to="0" class="active"></li>
                <li data-target="#carouselExampleIndicators" data-slide-to="1"></li>
                <li data-target="#carouselExampleIndicators" data-slide-to="2"></li>
                <li data-target="#carouselExampleIndicators" data-slide-to="3"></li>
                <li data-target="#carouselExampleIndicators" data-slide-to="4"></li>
                <li data-target="#carouselExampleIndicators" data-slide-to="5"></li>
            </ol>
            <div class="carousel-inner">
                <div class="carousel-item active">
                    <img class="d-block w-100" src="/uploads/pavbhaji.png" alt="First slide">
                </div>
                <div class="carousel-item">
                    <img class="d-block w-100" src="/uploads/pizza.png" alt="Second slide">
                </div>
                <div class="carousel-item">
                    <img class="d-block w-100" src="/uploads/Idli.png" alt="Third slide">
                </div>
                <div class="carousel-item">
                    <img class="d-block w-100" src="/uploads/Chowmein.png" alt="fourth slide">
                </div>
                <div class="carousel-item">
                    <img class="d-block w-100" src="/uploads/Pasta.png" alt="fifth slide">
                </div>
                <div class="carousel-item">
                    <img class="d-block w-100" src="/uploads/Glasses.png" alt="Sixth slide">
                </div>
            </div>

        </div>
        <form action="/listings/new" method="GET">
            <h1>Welcome to our <span> Zaika Zunction </span></h1>
            <h2>Wanted to add More ?</h2>
            <button type="submit" id="createNew">Create new Food Item</button>
        </form>

        <ul class="foodList">
            <% if (allFoodListing && allFoodListing.length> 0) { %>
                <% for(let listing of allFoodListing){ %>
                    <li>
                        <a href="/listings/<%= listing._id %>">
                            <div class="card" style="width: 19rem;">
                                <img class="card-img-top" src="<%=listing.images.url%>" alt="FoodImage">
                                <div class="card-body">

                                    <p class="card-text"><b>
                                            <%=listing.name%>
                                        </b>
                                    </p>
                                    <!-- <p class="card-text" id="order"> -->
                                    <form action="/orders/<%= listing._id %>/order" method="POST">
                                        <button id="orders" type="submit">Order Now</button>
                                    </form>
                                    </p>
                                </div>
                            </div>
                        </a>
                    </li>
                    <br><br><br><br><br>
                    <br><br><br><br><br>
                    <br><br><br><br><br>
                    <hr>
                    <% } %>
                        <% } else { %>
                            <li>No listings found.</li>
                            <% } %>
        </ul>
        <div id="carouselExampleFade" class="carousel slide carousel-fade" data-ride="carousel">
            <div class="carousel-inner">
                <div class="carousel-item active">
                    <img src="/uploads/FoodSwiper2.png" class="d-block w-100" alt="...">
                </div>
                <div class="carousel-item">
                    <img src="/uploads/SwipedFood.png" class="d-block w-100" alt="...">
                </div>
                <div class="carousel-item">
                    <img src="/uploads/FoodSwiped3.png" class="d-block w-100" alt="...">
                </div>
            </div>
            <a class="carousel-control-prev" href="#carouselExampleFade" role="button" data-slide="prev">
                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                <span class="sr-only">Previous</span>
            </a>
            <a class="carousel-control-next" href="#carouselExampleFade" role="button" data-slide="next">
                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                <span class="sr-only">Next</span>
            </a>
        </div>
        <br>
        <br>
        <div class="container">
            <br>
            <h1 style="font-weight: bold;">Best Places to Eat Across Cities</h1>
            <br>
            <div class="row">
                <div class="col-3">
                    <ul class="restaurants">
                        <li>Best Restaurant in Bangalore</li>
                        <li>Best Restaurant in Hyderabad</li>
                        <li>Best Restaurant in Ahemedabad</li>
                    </ul>
                </div>
                <div class="col-3">
                    <ul class="restaurants">

                        <li>Best Restaurant in Kochi</li>
                        <li>Best Restaurant in Haryana</li>
                        <li>Best Restaurant in Calicut</li>

                    </ul>
                </div>
                <div class="col-3">
                    <ul class="restaurants">
                        <li>Best Restaurant in Pune</li>
                        <li>Best Restaurant in Chennai</li>
                        <li>Best Restaurant in Nagpur</li>
                    </ul>
                </div>
                <div class="col-3">
                    <ul class="restaurants">
                        <li>Best Restaurant in Chandigrah</li>
                        <li>Best Restaurant in Delhi</li>
                        <p>
                            <button id="prim" class="btn btn-primary" type="button" data-toggle="collapse"
                                data-target="#collapseExample" aria-expanded="false" aria-controls="collapseExample">
                                Show More
                            </button>
                        </p>
                    </ul>
                </div>

                <div class="collapse" id="collapseExample">
                    <div class="cards">
                        <div class="row">
                            <div class="col-3">
                                <ul class="restaurants">
                                    <li>Best Restaurant in Bangalore</li>
                                    <li>Best Restaurant in Hyderabad</li>
                                    <li>Best Restaurant in Ahemedabad</li>
                                </ul>
                            </div>
                            <div class="col-3">
                                <ul class="restaurants">

                                    <li>Best Restaurant in Kochi</li>
                                    <li>Best Restaurant in Haryana</li>
                                    <li>Best Restaurant in Calicut</li>

                                </ul>
                            </div>
                            <div class="col-3">
                                <ul class="restaurants">
                                    <li>Best Restaurant in Pune</li>
                                    <li>Best Restaurant in Chennai</li>
                                    <li>Best Restaurant in Nagpur</li>
                                </ul>
                            </div>
                            <div class="col-3">
                                <ul class="restaurants">
                                    <li>Best Restaurant in Chandigrah</li>
                                    <li>Best Restaurant in Delhi</li>
                                    <li>Best Restaurant in Gurgaon</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <br>
            <h1 style="font-weight: bold;">Best Cuisines Near Me</h1>
            <br>
            <div class="row">
                <div class="col-3">
                    <ul class="restaurants">
                        <li>Chinese Restaurants near Me</li>
                        <li>Korean Restaurants near Me</li>
                        <li>Indian Restaurants near Me</li>
                    </ul>
                </div>
                <div class="col-3">
                    <ul class="restaurants">

                        <li>Chinese Restaurants near Me</li>
                        <li>Chinese Restaurants near Me</li>
                        <li>Chinese Restaurants near Me</li>

                    </ul>
                </div>
                <div class="col-3">
                    <ul class="restaurants">
                        <li>Chinese Restaurants near Me</li>
                        <li>Chinese Restaurants near Me</li>
                        <li>Chinese Restaurants near Me</li>
                    </ul>
                </div>
                <div class="col-3">
                    <ul class="restaurants">
                        <li>Chinese Restaurants near Me</li>
                        <li>Chinese Restaurants near Me</li>
                        <p>
                            <button class="btn btn-success" id="succ" type="button" data-toggle="collapse"
                                data-target="#collapseExamples" aria-expanded="false" aria-controls="collapseExample">
                                Show More
                            </button>
                        </p>

                    </ul>
                </div>

                <div class="collapse" id="collapseExamples">
                    <div class="cards">
                        <div class="row">
                            <div class="col-3">
                                <ul class="restaurants">
                                    <li>Best Restaurant in Bangalore</li>
                                    <li>Best Restaurant in Hyderabad</li>
                                    <li>Best Restaurant in Ahemedabad</li>
                                </ul>
                            </div>
                            <div class="col-3">
                                <ul class="restaurants">

                                    <li>Best Restaurant in Kochi</li>
                                    <li>Best Restaurant in Haryana</li>
                                    <li>Best Restaurant in Calicut</li>

                                </ul>
                            </div>
                            <div class="col-3">
                                <ul class="restaurants">
                                    <li>Best Restaurant in Pune</li>
                                    <li>Best Restaurant in Chennai</li>
                                    <li>Best Restaurant in Nagpur</li>
                                </ul>
                            </div>
                            <div class="col-3">
                                <ul class="restaurants">
                                    <li>Best Restaurant in Chandigrah</li>
                                    <li>Best Restaurant in Delhi</li>
                                    <li>Best Restaurant in Gurgaon</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            const sloganText = document.getElementById('zaika-slogan').innerText;
            const words = sloganText.split(' ');

            document.getElementById('zaika-slogan').innerHTML = '';
            words.forEach((word, index) => {
                const span = document.createElement('span');
                span.innerText = word + ' ';
                span.classList.add(index % 2 === 0 ? 'alt-color-1' : 'alt-color-2');
                document.getElementById('zaika-slogan').appendChild(span);
            });

            const carousel = document.querySelector('#carouselExampleIndicators');
            let isPaused = false;
            let delay = 2000;
            let interval = 3000;

            function startCarousel() {
                $(carousel).carousel('cycle');
            }

            function pauseAndDelay() {
                $(carousel).carousel('pause');
                setTimeout(() => {
                    startCarousel();
                }, delay);
            }

            carousel.addEventListener('mouseenter', () => {
                isPaused = true;
                $(carousel).carousel('pause');
            });

            carousel.addEventListener('mouseleave', () => {
                isPaused = false;
                startCarousel();
            });

            $(carousel).carousel({ interval: interval });

            $(carousel).on('slid.bs.carousel', () => {
                if (!isPaused) {
                    pauseAndDelay();
                }
            });
            startCarousel();
            let lastScrollY = window.scrollY;

            function updateCardAnimations() {
                const cards = document.querySelectorAll('.card');
                const windowHeight = window.innerHeight;
                const currentScrollY = window.scrollY;

                cards.forEach(card => {
                    const rect = card.getBoundingClientRect();
                    const cardTop = rect.top;
                    const cardBottom = rect.bottom;

                    if (cardTop < windowHeight && cardBottom > 0) {
                        // The card is within the viewport
                        if (currentScrollY < lastScrollY) {
                            // Scrolling up: Increase the size
                            card.classList.add('scale-up');
                            card.classList.remove('scale-down');
                        } else {
                            // Scrolling down: Decrease the size
                            card.classList.add('scale-down');
                            card.classList.remove('scale-up');
                        }
                    } else {
                        // Remove scaling when the card is out of viewport
                        card.classList.remove('scale-up', 'scale-down');
                    }
                });

                lastScrollY = currentScrollY; // Update last scroll position
            }

            // Initialize the animation
            updateCardAnimations();

            // Update on scroll
            document.addEventListener('scroll', updateCardAnimations);

            // Update on resize
            window.addEventListener('resize', updateCardAnimations);

            // Update on page load
            window.addEventListener('load', updateCardAnimations);

        </script>
    </body>