<!DOCTYPE html>
<html lang="en">
<head> 
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(to right,#e8e3c9,white,#ede2ab);
        }

        .container {
            width: 80%;
            margin: 0 auto;
        }

        .header {
            margin-top: 1rem;
            text-align: center;
            background-color: #b8860b; /* Dark golden color */
            color: white;
            padding: 20px;
        }

        .header h1 {
            margin: 0;
        }

        .content {
            display: flex;
            margin-top: 20px;
        }

        .sidebar {
            width: 25%;
            background-color: #c8c8c8; /* Subtle background color */
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .sidebar ul {
            list-style: none;
            padding: 0;
        }

        .sidebar ul li {
            padding: 10px;
            color: #333;
            cursor: pointer;
            font-weight: bold;
        }

        .sidebar ul li.active, .sidebar ul li:hover {
            background-color: #eedc82; /* Light golden hover effect */
            color: #b8860b; /* Dark golden text */
        }

        .main-content {
            width: 75%;
            padding: 20px;
            background-color:  #ffe6a2;
            border-radius: 8px;
            margin-left: 20px;
            margin-bottom: 1rem;
        }

        .main-content h2 {
            color: #b8860b; /* Dark golden color for headings */
        }

        .faq-item {
            padding: 15px;
            border-bottom: 1px solid #ddd;
            cursor: pointer;
        }

        .faq-item:hover {
            background-color: #faf0db; /* Slightly darker background on hover */
        }

        .faq-question {
            background-color: #333;
            color: #fff;
            padding: 15px;
            border: none;
            width: 100%;
            text-align: left;
            font-size: 18px;
            cursor: pointer;
        }

        .faq-answer {
            display: none; /* Initially hidden */
            background-color: #f5f5f5;
            margin-top: 10px;
            padding: 15px;
            font-size: 16px;
        }

        .faq-answer.active {
            display: block; /* Show the answer when toggled */
        }
    </style>
</head>
<body>
    <% layout("/layouts/boilerplate") %>
<div class="container">
    <header class="header">
        <h1>Help & Support</h1>
        <p>Let's take a step ahead and help you better.</p>
    </header>

    <div class="content">
        <div class="sidebar">
            <ul>
                <li onclick="window.location.href='ii.html';" style="cursor:pointer;">Partner Onboarding</li>
                <li onclick="window.location.href='legal.html';" style="cursor:pointer;">Legal</li>
                <li class="active" onclick="window.location.href='FAQS.html';" style="cursor:pointer;">FAQS</li>
            </ul>
        </div>

        <div class="main-content">
            <h2 style="color: black; font-weight: bold;">FAQS</h2>

            <div class="faq-item">
                <button class="faq-question">How to place an order?</button>
                <div class="faq-answer">
                    <p>To place an order, simply browse through the menu, select your items, and proceed to checkout. You can pay online or choose cash on delivery.</p>
                </div>
            </div>

            <div class="faq-item">
                <button class="faq-question">What are the payment options available?</button>
                <div class="faq-answer">
                    <p>We accept credit/debit cards, UPI, mobile wallets, and cash on delivery.</p>
                </div>
            </div>

            <div class="faq-item">
                <button class="faq-question">How can I contact customer support?</button>
                <div class="faq-answer">
                    <p>You can reach our customer support via the contact form on the "Contact Us" page or call us directly at our helpline.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // FAQ Toggle Functionality
    document.addEventListener('DOMContentLoaded', function() {
        const faqQuestions = document.querySelectorAll('.faq-question');

        faqQuestions.forEach((question) => {
            question.addEventListener('click', function() {
                const answer = this.nextElementSibling;
                answer.classList.toggle('active'); // Toggle the answer visibility
            });
        });
    });
</script>

</body>
</html>